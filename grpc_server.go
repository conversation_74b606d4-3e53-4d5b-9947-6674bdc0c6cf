package main

import (
	"log"
	"net"

	"google.golang.org/grpc"
	"github.com/goravel/framework/facades"

	"goravel/bootstrap"
	"goravel/app/grpc/controllers"
	"goravel/app/grpc/protos"
)

func main() {
	// Bootstrap the framework
	bootstrap.Boot()

	// Get gRPC configuration
	host := facades.Config().GetString("grpc.host", "127.0.0.1")
	port := facades.Config().GetString("grpc.port", "50051")
	address := host + ":" + port

	// Create listener
	listener, err := net.Listen("tcp", address)
	if err != nil {
		log.Fatalf("Failed to listen on %s: %v", address, err)
	}

	// Create gRPC server
	server := grpc.NewServer()

	// Register services
	userController := controllers.NewUserController()
	protos.RegisterUserServer(server, userController)

	log.Printf("gRPC server starting on %s", address)
	log.Printf("Press Ctrl+C to stop the server")

	// Start server
	if err := server.Serve(listener); err != nil {
		log.Fatalf("Failed to serve gRPC server: %v", err)
	}
}
