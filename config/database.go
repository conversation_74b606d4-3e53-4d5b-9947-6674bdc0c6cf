package config

import (
	"github.com/goravel/framework/contracts/database/driver"
	"github.com/goravel/framework/facades"
	postgresfacades "github.com/goravel/postgres/facades"
	"strings"
)

func init() {
	config := facades.Config()
	config.Add("database", map[string]any{
		// Default database connection name
		"default": config.Env("DB_CONNECTION", "postgres"),

		// Database connections
		"connections": map[string]any{
			"postgres": map[string]any{
				"host":     config.Env("DB_HOST", "127.0.0.1"),
				"port":     config.Env("DB_PORT", 5432),
				"database": config.Env("DB_DATABASE", "forge"),
				"username": config.Env("DB_USERNAME", ""),
				"password": config.Env("DB_PASSWORD", ""),
				"sslmode":  "disable",
				"prefix":   "",
				"singular": false,
				"schema":   config.Env("DB_SCHEMA", "public"),
				"via": func() (driver.Driver, error) {
					return postgresfacades.Postgres("postgres")
				},
			},
		},

		// Pool configuration
		"pool": map[string]any{
			// Sets the maximum number of connections in the idle
			// connection pool.
			//
			// If MaxOpenConns is greater than 0 but less than the new MaxIdleConns,
			// then the new MaxIdleConns will be reduced to match the MaxOpenConns limit.
			//
			// If n <= 0, no idle connections are retained.
			"max_idle_conns": 10,
			// Sets the maximum number of open connections to the database.
			//
			// If MaxIdleConns is greater than 0 and the new MaxOpenConns is less than
			// MaxIdleConns, then MaxIdleConns will be reduced to match the new
			// MaxOpenConns limit.
			//
			// If n <= 0, then there is no limit on the number of open connections.
			"max_open_conns": 100,
			// Sets the maximum amount of time a connection may be idle.
			//
			// Expired connections may be closed lazily before reuse.
			//
			// If d <= 0, connections are not closed due to a connection's idle time.
			// Unit: Second
			"conn_max_idletime": 3600,
			// Sets the maximum amount of time a connection may be reused.
			//
			// Expired connections may be closed lazily before reuse.
			//
			// If d <= 0, connections are not closed due to a connection's age.
			// Unit: Second
			"conn_max_lifetime": 3600,
		},
		// Redis configuration with Sentinel support
		"redis": map[string]any{
			// Default Redis connection
			"default": map[string]any{
				"cluster": map[string]any{
					"enable": true,
					"nodes": []string{
						"127.0.0.1:7000",
						"127.0.0.1:7001",
						"127.0.0.1:7002",
						// Thêm các node khác của cluster
					},
					"options": map[string]any{
						"max_retries":   2,
						"read_timeout":  "3s",
						"write_timeout": "3s",
					},
				},
				"host":     config.Env("REDIS_HOST", "127.0.0.1"),
				"password": config.Env("REDIS_PASSWORD", ""),
				"port":     config.Env("REDIS_PORT", 6379),
				"database": config.Env("REDIS_DB", 0),
			},

			// Session connection (optional separate DB)
			"session": map[string]any{
				"host":     config.Env("REDIS_HOST", "127.0.0.1"),
				"password": config.Env("REDIS_PASSWORD", ""),
				"port":     config.Env("REDIS_PORT", 6379),
				"database": config.Env("REDIS_SESSION_DB", 2),
			},

			// Queue connection (optional separate DB)
			"queue": map[string]any{
				"host":     config.Env("REDIS_HOST", "127.0.0.1"),
				"password": config.Env("REDIS_PASSWORD", ""),
				"port":     config.Env("REDIS_PORT", 6379),
				"database": config.Env("REDIS_QUEUE_DB", 3),
			},
		},

		// Sets the threshold for slow queries in milliseconds, the slow query will be logged.
		// Unit: Millisecond
		"slow_threshold": 200,

		// Migration Repository Table
		//
		// This table keeps track of all the migrations that have already run for
		// your application. Using this information, we can determine which of
		// the migrations on disk haven't actually been run in the database.
		"migrations": map[string]any{
			"table": "migrations",
		},
	})
}

func parseSentinels(sentinelsStr string) []string {
	if sentinelsStr == "" {
		return []string{}
	}

	sentinels := make([]string, 0)
	for _, sentinel := range strings.Split(sentinelsStr, ",") {
		if trimmed := strings.TrimSpace(sentinel); trimmed != "" {
			sentinels = append(sentinels, trimmed)
		}
	}
	return sentinels
}
