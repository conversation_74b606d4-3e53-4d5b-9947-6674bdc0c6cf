package routes

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/facades"
	"github.com/goravel/framework/support"
	"goravel/app/http/controllers"
)

func Web() {
	facades.Route().Get("/", func(ctx http.Context) http.Response {
		return ctx.Response().View().Make("welcome.tmpl", map[string]any{
			"version": support.Version,
		})
	})

	userController := controllers.NewUserController()

	facades.Route().Post("/users", userController.Store)

	facades.Route().Get("/db-ping", func(ctx http.Context) http.Response {
		var result int
		// Query đơn giản SELECT 1
		err := facades.Orm().Query().Raw("SELECT 1").Scan(&result)
		if err != nil {
			return ctx.Response().<PERSON>son(500, http.Json{
				"error": err.Error(),
			})
		}

		return ctx.Response().<PERSON><PERSON>(200, http.<PERSON><PERSON>{
			"ping": result,
		})
	})

	facades.Route().Get("/hello-world", func(ctx http.Context) http.Response {

		return ctx.Response().<PERSON><PERSON>(200, http.<PERSON>son{
			"msg": "hello-world",
		})
	})
}
