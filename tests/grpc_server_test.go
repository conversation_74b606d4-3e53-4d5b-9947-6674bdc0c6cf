package main

import (
	"fmt"
	"log"
	"net"
	"time"

	"google.golang.org/grpc"
	"github.com/goravel/framework/facades"

	"goravel/bootstrap"
	"goravel/app/grpc/controllers"
	"goravel/app/grpc/protos"
)

func main() {
	fmt.Println("========================================")
	fmt.Println("       Simple gRPC Server Test")
	fmt.Println("========================================")

	// Bootstrap the framework
	fmt.Println("Bootstrapping Goravel...")
	bootstrap.Boot()

	// Get configuration
	host := facades.Config().GetString("grpc.host", "127.0.0.1")
	port := facades.Config().GetString("grpc.port", "50051")
	address := host + ":" + port

	fmt.Printf("Starting gRPC server on %s\n", address)

	// Create listener
	listener, err := net.Listen("tcp", address)
	if err != nil {
		log.Fatalf("Failed to listen on %s: %v", address, err)
	}

	// Create gRPC server
	server := grpc.NewServer()

	// Register services
	userController := controllers.NewUserController()
	protos.RegisterUserServer(server, userController)

	fmt.Println("gRPC server is ready!")
	fmt.Println("You can now run: go run examples/grpc_client.go")
	fmt.Println("Press Ctrl+C to stop")

	// Start server in background
	go func() {
		if err := server.Serve(listener); err != nil {
			log.Fatalf("Failed to serve gRPC server: %v", err)
		}
	}()

	// Keep running for 30 seconds for testing
	time.Sleep(30 * time.Second)
	fmt.Println("Test completed. Shutting down...")
	server.GracefulStop()
}
