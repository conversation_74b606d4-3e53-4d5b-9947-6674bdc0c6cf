# Goravel gRPC Demo

Đây là ví dụ mẫu sử dụng gRPC trong Goravel framework.

## Cấu trúc Project

```
app/grpc/
├── controllers/
│   └── user_controller.go    # gRPC controller
├── protos/
│   ├── user.proto           # Protocol Buffer definition
│   ├── user.pb.go           # Generated Go code
│   └── user_grpc.pb.go      # Generated gRPC code
├── interceptors/            # gRPC interceptors
└── kernel.go               # gRPC kernel configuration

routes/
└── grpc.go                 # gRPC routes registration

examples/
└── grpc_client.go          # Demo client

config/
└── grpc.go                 # gRPC configuration
```

## Chạy Demo

### Cách 1: Sử dụng script tự động
```bash
# Trên Windows
run_grpc_demo.bat
```

### Cách 2: Chạy thủ công

1. **Khởi động gRPC server:**
```bash
go run . grpc:start
```

2. **Chạy client demo (terminal khác):**
```bash
go run examples/grpc_client.go
```

## <PERSON><PERSON>u hình

Server gRPC chạy trên:
- Host: 127.0.0.1
- Port: 50051

Có thể thay đổi trong file `.env`:
```
GRPC_HOST=127.0.0.1
GRPC_PORT=50051
```

## API gRPC

### User Service

**Method:** `Show`
- **Request:** `UserRequest`
  - `id` (int64): User ID
- **Response:** `UserResponse`
  - `code` (int32): Response code
  - `message` (string): Response message
  - `name` (string): User name

**Ví dụ:**
```
Request: { id: 1 }
Response: { code: 200, message: "Success", name: "User_1" }
```

## Tùy chỉnh

### Thêm method mới vào proto file:
1. Sửa `app/grpc/protos/user.proto`
2. Generate lại Go code:
```bash
protoc --go_out=app/grpc/protos --go-grpc_out=app/grpc/protos --proto_path=app/grpc/protos user.proto
```
3. Implement method trong controller
4. Test với client

### Thêm service mới:
1. Tạo proto file mới
2. Generate Go code
3. Tạo controller mới
4. Register trong `app/grpc/kernel.go`
5. Update routes trong `routes/grpc.go`

## Troubleshooting

- **Server không khởi động:** Kiểm tra port 50051 có bị chiếm không
- **Client không kết nối được:** Đảm bảo server đã khởi động và cấu hình đúng host/port
- **Generate proto thất bại:** Cài đặt protoc và protoc-gen-go, protoc-gen-go-grpc
