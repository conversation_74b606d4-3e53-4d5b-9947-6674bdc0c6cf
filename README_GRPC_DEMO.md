# Goravel gRPC Demo

Đây là ví dụ mẫu sử dụng gRPC trong Goravel framework.

## Cấu trúc Project

```
app/grpc/
├── controllers/
│   └── user_controller.go    # gRPC controller
├── protos/
│   ├── user.proto           # Protocol Buffer definition
│   ├── user.pb.go           # Generated Go code
│   └── user_grpc.pb.go      # Generated gRPC code
├── interceptors/            # gRPC interceptors
└── kernel.go               # gRPC kernel configuration

routes/
└── grpc.go                 # gRPC routes registration

examples/
└── grpc_client.go          # Demo client

config/
└── grpc.go                 # gRPC configuration
```

## Chạy Demo

### Cách 1: Test hoàn chỉnh (K<PERSON><PERSON><PERSON><PERSON> nghị)
```bash
# Test toàn bộ hệ thống
test_grpc_complete.bat
```

### Cách 2: Test đơn giản
```bash
# Terminal 1: Khởi động simple server
go run tests/grpc_server_test.go

# Terminal 2: Test client
go run examples/grpc_client.go
```

### Cách 3: Sử dụng Goravel framework
```bash
# Terminal 1: Khởi động Goravel (HTTP + gRPC)
go run .

# Terminal 2: Test client
go run examples/grpc_client.go
```

### Cách 4: Script tự động
```bash
run_grpc_demo.bat
```

## Cấu hình

Goravel khởi động đồng thời:
- **HTTP server:** 127.0.0.1:8000 (từ APP_HOST và APP_PORT)
- **gRPC server:** 127.0.0.1:50051 (từ GRPC_HOST và GRPC_PORT)

Có thể thay đổi trong file `.env`:
```
# HTTP Server
APP_HOST=127.0.0.1
APP_PORT=8000

# gRPC Server
GRPC_HOST=127.0.0.1
GRPC_PORT=50051
```

**Lưu ý:** Goravel sử dụng `facades.Grpc().Run()` để khởi động gRPC server tự động cùng với HTTP server trong `main.go`.

## API gRPC

### User Service

**Method:** `Show`
- **Request:** `UserRequest`
  - `id` (int64): User ID
- **Response:** `UserResponse`
  - `code` (int32): Response code
  - `message` (string): Response message
  - `name` (string): User name

**Ví dụ:**
```
Request: { id: 1 }
Response: { code: 200, message: "Success", name: "User_1" }
```

## Tùy chỉnh

### Thêm method mới vào proto file:
1. Sửa `app/grpc/protos/user.proto`
2. Generate lại Go code:
```bash
protoc --go_out=app/grpc/protos --go-grpc_out=app/grpc/protos --proto_path=app/grpc/protos user.proto
```
3. Implement method trong controller
4. Test với client

### Thêm service mới:
1. Tạo proto file mới
2. Generate Go code
3. Tạo controller mới
4. Register trong `app/grpc/kernel.go`
5. Update routes trong `routes/grpc.go`

## Troubleshooting

### Lỗi "connection error: dial tcp 127.0.0.1:50051: connectex: No connection could be made"

**Nguyên nhân:** gRPC server chưa được khởi động.

**Giải pháp:**
1. **Kiểm tra Go installation:**
   ```bash
   go version
   ```
   Nếu báo lỗi "go is not recognized", cần cài đặt Go từ https://golang.org/dl/

2. **Khởi động server trước:**
   ```bash
   # Cách 1: Sử dụng script
   start_grpc_server.bat

   # Cách 2: Command line
   go run .
   ```

3. **Kiểm tra server đã chạy:**
   ```bash
   netstat -an | findstr ":50051"
   ```

4. **Chạy client (terminal khác):**
   ```bash
   # Cách 1: Sử dụng script
   run_grpc_client.bat

   # Cách 2: Command line
   go run examples/grpc_client.go
   ```

### Các lỗi khác

- **"go is not recognized":** Cài đặt Go và thêm vào PATH
- **Port 50051 bị chiếm:** Thay đổi GRPC_PORT trong .env
- **Generate proto thất bại:** Cài đặt protoc và protoc-gen-go, protoc-gen-go-grpc
- **Import error:** Chạy `go mod tidy` để cập nhật dependencies
