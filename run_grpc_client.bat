@echo off
echo ========================================
echo       Running gRPC Client Demo
echo ========================================

echo.
echo Checking Go installation...

where go >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Go is not installed or not in PATH
    echo Please install Go from https://golang.org/dl/
    echo Or add Go to your PATH environment variable
    pause
    exit /b 1
)

echo.
echo Checking if gRPC server is running...
netstat -an | findstr ":50051" >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: gRPC server might not be running on port 50051
    echo Please start the server first using: start_grpc_server.bat
    echo.
    echo Continuing anyway...
    echo.
)

echo Running gRPC client demo...
echo.

go run examples/grpc_client.go

echo.
echo Demo completed!
pause
