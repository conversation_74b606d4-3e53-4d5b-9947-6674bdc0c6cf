# Debug Guide - Goravel gRPC

## 🔍 Lỗi "connection refused" - Hướng dẫn debug chi tiết

### Bước 1: Ki<PERSON>m tra Go installation
```bash
go version
```
**Kết quả mong đợi:** `go version go1.23.x windows/amd64`

**Nếu lỗi:** Cài đặt Go từ https://golang.org/dl/

### Bước 2: Kiểm tra project build
```bash
# Test build
test_build.bat

# Hoặc manual
go mod tidy
go build .
```

### Bước 3: Test gRPC đơn giản
```bash
# Chạy test hoàn chỉnh
test_grpc_complete.bat
```

### Bước 4: Debug từng bước

#### 4.1 Test Simple gRPC Server
```bash
# Terminal 1
go run tests/grpc_server_test.go
```
**Kết quả mong đợi:**
```
========================================
       Simple gRPC Server Test
========================================
Bootstrapping Goravel...
Starting gRPC server on 127.0.0.1:50051
gRPC server is ready!
```

#### 4.2 Test Client
```bash
# Terminal 2 (khi server đang chạy)
go run examples/grpc_client.go
```
**Kết quả mong đợi:**
```
=== Testing User ID: 1 ===
Response:
  Code: 200
  Message: Success
  Name: User_1
```

#### 4.3 Test Goravel Framework
```bash
# Terminal 1
go run .
```
**Kết quả mong đợi:**
```
[HTTP server starting logs]
[gRPC server starting logs]
```

### Bước 5: Kiểm tra port
```bash
# Kiểm tra port 50051 có đang được sử dụng
netstat -an | findstr ":50051"
```
**Kết quả mong đợi:** `TCP    127.0.0.1:50051    0.0.0.0:0    LISTENING`

## 🛠️ Các lỗi thường gặp

### Lỗi 1: "go is not recognized"
**Nguyên nhân:** Go chưa cài đặt hoặc không có trong PATH
**Giải pháp:** 
- Cài đặt Go từ https://golang.org/dl/
- Hoặc sử dụng GoLand IDE

### Lỗi 2: "connection refused"
**Nguyên nhân:** gRPC server chưa khởi động
**Giải pháp:**
1. Chạy `test_grpc_simple.go` trước
2. Đảm bảo thấy message "gRPC server is ready!"
3. Sau đó mới chạy client

### Lỗi 3: "port already in use"
**Nguyên nhân:** Port 50051 đã bị chiếm
**Giải pháp:**
1. Tìm process đang sử dụng: `netstat -ano | findstr ":50051"`
2. Kill process: `taskkill /PID <process_id> /F`
3. Hoặc đổi port trong .env: `GRPC_PORT=50052`

### Lỗi 4: "build failed"
**Nguyên nhân:** Dependencies hoặc code có vấn đề
**Giải pháp:**
1. Chạy `go mod tidy`
2. Kiểm tra import paths
3. Đảm bảo proto files đã được generate

## 📋 Checklist debug

- [ ] Go đã cài đặt và có trong PATH
- [ ] Project build thành công (`go build .`)
- [ ] Proto files đã được generate
- [ ] Port 50051 không bị chiếm
- [ ] Server khởi động thành công
- [ ] Client kết nối được

## 🎯 Test nhanh

Chạy lệnh này để test toàn bộ:
```bash
test_grpc_complete.bat
```

Nếu tất cả đều OK, bạn sẽ thấy:
```
✓ Go installation: OK
✓ Project build: OK  
✓ gRPC server: Check output above
✓ gRPC client: Check output above
```
