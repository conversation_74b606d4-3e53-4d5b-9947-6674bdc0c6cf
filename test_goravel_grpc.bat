@echo off
echo ========================================
echo      Testing Goravel gRPC
echo ========================================

echo.
echo Checking Go installation...
where go >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Go is not installed or not in PATH
    pause
    exit /b 1
)

echo ✓ Go found!
echo.

echo Starting Goravel application...
echo This will start both HTTP (port 8000) and gRPC (port 50051) servers
echo.
echo Watch for any error messages...
echo Press Ctrl+C to stop when ready to test client
echo.

go run .

pause
