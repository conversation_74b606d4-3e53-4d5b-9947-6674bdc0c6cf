APP_NAME=Goravel
APP_ENV=local
APP_KEY=vn7wc1lWu9TDKOo56o31OzPqCqxUozVc
APP_DEBUG=true
APP_URL=http://localhost
APP_HOST=127.0.0.1
APP_PORT=8000

GRPC_HOST=
GRPC_PORT=

JWT_SECRET=

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=postgres
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=goravel
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=file
SESSION_LIFETIME=120

# Mode: single | sentinel | cluster
REDIS_MODE=sentinel

# Single node
REDIS_HOST=127.0.0.1
REDIS_PORT=6379

# Cluster
REDIS_CLUSTER_ENABLED=true
REDIS_CLUSTER_NODES=127.0.0.1:7000,127.0.0.1:7001,127.0.0.1:7002

# Common
REDIS_PASSWORD=
REDIS_DB=0

MAIL_HOST=
MAIL_PORT=
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_FROM_ADDRESS=
MAIL_FROM_NAME=
