@echo off
echo ========================================
echo         Goravel gRPC Demo
echo ========================================

echo.
echo Starting gRPC server...
echo Server will run on 127.0.0.1:50051
echo.

start "Goravel gRPC Server" cmd /k "go run grpc_server.go && pause"

echo Waiting for server to start...
timeout /t 3 /nobreak > nul

echo.
echo Starting gRPC client demo...
echo.

go run examples/grpc_client.go

echo.
echo Demo completed!
echo Press any key to close server...
pause > nul

taskkill /f /fi "WindowTitle eq Goravel gRPC Server*" > nul 2>&1
