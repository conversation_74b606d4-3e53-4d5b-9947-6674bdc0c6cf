// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: app/grpc/protos/user.proto

package protos

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserRequest) Reset() {
	*x = UserRequest{}
	mi := &file_app_grpc_protos_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRequest) ProtoMessage() {}

func (x *UserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_app_grpc_protos_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRequest.ProtoReflect.Descriptor instead.
func (*UserRequest) Descriptor() ([]byte, []int) {
	return file_app_grpc_protos_user_proto_rawDescGZIP(), []int{0}
}

func (x *UserRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserResponse) Reset() {
	*x = UserResponse{}
	mi := &file_app_grpc_protos_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserResponse) ProtoMessage() {}

func (x *UserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_app_grpc_protos_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserResponse.ProtoReflect.Descriptor instead.
func (*UserResponse) Descriptor() ([]byte, []int) {
	return file_app_grpc_protos_user_proto_rawDescGZIP(), []int{1}
}

func (x *UserResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UserResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UserResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_app_grpc_protos_user_proto protoreflect.FileDescriptor

const file_app_grpc_protos_user_proto_rawDesc = "" +
	"\n" +
	"\x1aapp/grpc/protos/user.proto\x12\x06protos\"\x1d\n" +
	"\vUserRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"P\n" +
	"\fUserResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name29\n" +
	"\x04User\x121\n" +
	"\x04Show\x12\x13.protos.UserRequest\x1a\x14.protos.UserResponseB'Z%github.com/goravel/grpc/protos;protosb\x06proto3"

var (
	file_app_grpc_protos_user_proto_rawDescOnce sync.Once
	file_app_grpc_protos_user_proto_rawDescData []byte
)

func file_app_grpc_protos_user_proto_rawDescGZIP() []byte {
	file_app_grpc_protos_user_proto_rawDescOnce.Do(func() {
		file_app_grpc_protos_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_app_grpc_protos_user_proto_rawDesc), len(file_app_grpc_protos_user_proto_rawDesc)))
	})
	return file_app_grpc_protos_user_proto_rawDescData
}

var file_app_grpc_protos_user_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_app_grpc_protos_user_proto_goTypes = []any{
	(*UserRequest)(nil),  // 0: protos.UserRequest
	(*UserResponse)(nil), // 1: protos.UserResponse
}
var file_app_grpc_protos_user_proto_depIdxs = []int32{
	0, // 0: protos.User.Show:input_type -> protos.UserRequest
	1, // 1: protos.User.Show:output_type -> protos.UserResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_app_grpc_protos_user_proto_init() }
func file_app_grpc_protos_user_proto_init() {
	if File_app_grpc_protos_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_app_grpc_protos_user_proto_rawDesc), len(file_app_grpc_protos_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_app_grpc_protos_user_proto_goTypes,
		DependencyIndexes: file_app_grpc_protos_user_proto_depIdxs,
		MessageInfos:      file_app_grpc_protos_user_proto_msgTypes,
	}.Build()
	File_app_grpc_protos_user_proto = out.File
	file_app_grpc_protos_user_proto_goTypes = nil
	file_app_grpc_protos_user_proto_depIdxs = nil
}
