@echo off
echo ========================================
echo      Starting Goravel gRPC Server
echo ========================================

echo.
echo Checking Go installation...

where go >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Go is not installed or not in PATH
    echo Please install Go from https://golang.org/dl/
    echo Or add Go to your PATH environment variable
    pause
    exit /b 1
)

echo Go found! Starting gRPC server...
echo Server will run on 127.0.0.1:50051
echo.
echo Press Ctrl+C to stop the server
echo.

go run grpc_server.go

pause
