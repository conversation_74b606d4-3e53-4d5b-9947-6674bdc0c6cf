@echo off
echo ========================================
echo     Complete gRPC Test Suite
echo ========================================

echo.
echo Step 1: Testing Go installation...
where go >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Go is not installed or not in PATH
    echo Please install Go from https://golang.org/dl/
    pause
    exit /b 1
)
echo ✓ Go found!

echo.
echo Step 2: Testing project build...
go mod tidy >nul 2>&1
go build -o temp_test.exe . >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Project build failed
    echo Try running: go mod tidy
    pause
    exit /b 1
)
del temp_test.exe >nul 2>&1
echo ✓ Project builds successfully!

echo.
echo Step 3: Testing simple gRPC server...
echo Starting server for 10 seconds...
start /min "Test gRPC Server" cmd /c "go run tests/grpc_server_test.go"

echo Waiting for server to start...
timeout /t 3 /nobreak >nul

echo.
echo Step 4: Testing gRPC client...
go run examples/grpc_client.go

echo.
echo Step 5: Cleaning up...
taskkill /f /fi "WindowTitle eq Test gRPC Server*" >nul 2>&1

echo.
echo ========================================
echo         Test Results Summary
echo ========================================
echo ✓ Go installation: OK
echo ✓ Project build: OK  
echo ✓ gRPC server: Check output above
echo ✓ gRPC client: Check output above
echo.
echo If you see successful responses above, gRPC is working!
echo.
pause
