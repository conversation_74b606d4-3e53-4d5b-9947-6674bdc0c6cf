@echo off
echo ========================================
echo       Testing Goravel Build
echo ========================================

echo.
echo Checking Go installation...

where go >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Go is not installed or not in PATH
    echo Please install Go from https://golang.org/dl/
    pause
    exit /b 1
)

echo Go found! Testing build...
echo.

echo Testing go mod tidy...
go mod tidy
if %errorlevel% neq 0 (
    echo ERROR: go mod tidy failed
    pause
    exit /b 1
)

echo.
echo Testing go build...
go build -o test_build.exe .
if %errorlevel% neq 0 (
    echo ERROR: go build failed
    pause
    exit /b 1
)

echo.
echo Build successful! Cleaning up...
del test_build.exe

echo.
echo All tests passed!
pause
