# Hướng dẫn Setup và Chạy gRPC Demo

## 🔧 <PERSON><PERSON><PERSON> cầu hệ thống

1. **Go Programming Language**
   - Download từ: https://golang.org/dl/
   - Phiên bản: Go 1.23+ (theo go.mod)
   - <PERSON><PERSON><PERSON> bảo Go có trong PATH

2. **Protocol Buffers Compiler (protoc)**
   - <PERSON><PERSON> có sẵn trong hệ thống của bạn
   - Version: libprotoc 31.1

## 🚀 Cách chạy demo

### Phương pháp 1: Sử dụng GoLand IDE (Khuyến nghị)

1. **Mở project trong GoLand**
2. **Chạy main.go:**
   - Click chuột phải vào `main.go`
   - <PERSON><PERSON>n "Run 'go build main.go'"
   - Hoặc sử dụng shortcut Ctrl+Shift+F10

3. **Kiểm tra log:**
   ```
   HTTP server starting on 127.0.0.1:8000
   gRPC server starting on 127.0.0.1:50051
   ```

4. **Chạy client demo (terminal mới trong GoLand):**
   ```bash
   go run examples/grpc_client.go
   ```

### Phương pháp 2: Command Line (nếu Go có trong PATH)

1. **Khởi động server:**
   ```bash
   go run .
   ```

2. **Chạy client (terminal khác):**
   ```bash
   go run examples/grpc_client.go
   ```

### Phương pháp 3: Sử dụng Scripts (Windows)

1. **Khởi động server:**
   ```bash
   start_grpc_server.bat
   ```

2. **Chạy client:**
   ```bash
   run_grpc_client.bat
   ```

## ✅ Kết quả mong đợi

Khi chạy client, bạn sẽ thấy:

```
=== Testing User ID: 1 ===
Response:
  Code: 200
  Message: Success
  Name: User_1

=== Testing User ID: 2 ===
Response:
  Code: 200
  Message: Success
  Name: User_2

=== Testing User ID: 3 ===
Response:
  Code: 200
  Message: Success
  Name: User_3

=== Testing User ID: 0 ===
Response:
  Code: 400
  Message: Invalid user ID
  Name: 

=== Testing User ID: -1 ===
Response:
  Code: 400
  Message: Invalid user ID
  Name: 

=== gRPC Client Demo Completed ===
```

## 🔍 Troubleshooting

### Lỗi "go is not recognized"
- **Giải pháp:** Sử dụng GoLand IDE để chạy
- **Hoặc:** Cài đặt Go và thêm vào PATH

### Lỗi "connection refused"
- **Nguyên nhân:** Server chưa khởi động
- **Giải pháp:** Đảm bảo `go run .` đang chạy trước khi chạy client

### Port bị chiếm
- **Kiểm tra:** `netstat -an | findstr ":50051"`
- **Giải pháp:** Thay đổi GRPC_PORT trong .env

## 📁 Files quan trọng

- `main.go` - Khởi động cả HTTP và gRPC server
- `app/grpc/controllers/user_controller.go` - gRPC controller
- `app/grpc/protos/user.proto` - Protocol definition
- `examples/grpc_client.go` - Demo client
- `.env` - Configuration (GRPC_HOST, GRPC_PORT)

## 🎯 Kết luận

Demo này cho thấy:
1. Cách setup gRPC trong Goravel framework
2. Cách tạo proto file và generate Go code
3. Cách implement gRPC controller
4. Cách khởi động server và test với client
5. Cách Goravel tích hợp gRPC với HTTP server
