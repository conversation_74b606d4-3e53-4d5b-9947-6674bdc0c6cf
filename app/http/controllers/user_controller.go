package controllers

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/facades"
	"github.com/goravel/framework/support/debug"
	"goravel/app/http/requests"
)

type UserController struct {
	// Dependent services
}

func NewUserController() *UserController {
	return &UserController{
		// Inject services
	}
}

type SuccessResponse struct {
	Hello   string         `json:"hello"`
	Name    string         `json:"name"`
	Hobbies []int          `json:"hobbies"`
	Address map[string]any `json:"address"`
}

func (r *UserController) Show(ctx http.Context) http.Response {
	facades.Log().Info("Input: ", ctx.Request().All())
	res := SuccessResponse{
		Hello:   "world",
		Name:    "Goravel",
		Hobbies: []int{1, 2, 3},
		Address: map[string]any{
			"city":   "Beijing",
			"street": "Chang'an Street",
		},
	}

	// Status 200 OK + trả JSO<PERSON> từ struct
	return ctx.Response().Json(http.StatusOK, res)
}

func (r *UserController) Store(ctx http.Context) http.Response {
	var storePost = requests.StorePostRequest{}
	errors, _ := ctx.Request().ValidateRequest(&storePost)

	if errors != nil {
		return ctx.Response().Json(http.StatusBadRequest, http.Json{
			"errors": errors.One(),
			"code":   422,
		})
	}

	println("enter")
	debug.Dump(storePost)

	// Status 200 OK + trả JSON từ struct
	return ctx.Response().Json(http.StatusOK, http.Json{
		"data": storePost,
		"code": 200,
	})
}

func (r *UserController) ShowCustomers(ctx http.Context) http.Response {
	res111 := SuccessResponse{
		Hello:   "world",
		Name:    "Goravel",
		Hobbies: []int{1, 2, 3},
		Address: map[string]any{
			"city":   "Beijing",
			"street": "Chang'an Street",
		},
	}

	facades.Log().Code("Debug hach toan").Hint("Hach toan mpos").In("Tien ve bao co").With(ctx.Request().All()).Info("Input: ", res111)
	res := SuccessResponse{
		Hello:   "world",
		Name:    "Goravel",
		Hobbies: []int{1, 2, 3},
		Address: map[string]any{
			"city":   "Beijing",
			"street": "Chang'an Street",
		},
	}

	// Status 200 OK + trả JSON từ struct
	return ctx.Response().Json(http.StatusOK, res)
}
