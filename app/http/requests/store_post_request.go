package requests

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/contracts/validation"
)

type StorePostRequest struct {
	Name  string `form:"name" json:"name"`
	Email string `form:"email" json:"email"`
}

func (r *StorePostRequest) Authorize(ctx http.Context) error {
	return nil
}

func (r *StorePostRequest) Filters(ctx http.Context) map[string]string {
	return map[string]string{}
}

func (r *StorePostRequest) Rules(ctx http.Context) map[string]string {
	return map[string]string{
		"name":  "required|max_len:255|min_len:4",
		"email": "required|email",
	}
}

func (r *StorePostRequest) Messages(ctx http.Context) map[string]string {
	return map[string]string{}
}

func (r *StorePostRequest) Attributes(ctx http.Context) map[string]string {
	return map[string]string{}
}

func (r *StorePostRequest) PrepareForValidation(ctx http.Context, data validation.Data) error {
	return nil
}
