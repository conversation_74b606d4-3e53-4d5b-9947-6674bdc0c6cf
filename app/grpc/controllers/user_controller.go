package controllers

import (
	"context"
	"fmt"

	"goravel/app/grpc/protos"
)

type UserController struct {
	protos.UnimplementedUserServer
}

func NewUserController() *UserController {
	return &UserController{}
}

func (r *UserController) Show(ctx context.Context, req *protos.UserRequest) (*protos.UserResponse, error) {
	// Simulate getting user data
	// In real application, you would fetch from database
	userID := req.GetId()
	
	if userID <= 0 {
		return &protos.UserResponse{
			Code:    400,
			Message: "Invalid user ID",
			Name:    "",
		}, nil
	}

	// Mock user data
	userName := fmt.Sprintf("User_%d", userID)
	
	return &protos.UserResponse{
		Code:    200,
		Message: "Success",
		Name:    userName,
	}, nil
}
