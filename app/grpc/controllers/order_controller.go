package controllers

import (
	"context"
	"goravel/app/grpc/protos"
)

type OrderController struct {
	protos.UnimplementedUserServer
}

func NewOrderController() *OrderController {
	return &OrderController{}
}

func (r *OrderController) Detail(ctx context.Context, req *protos.OrderRequest) (*protos.OrderResponse, error) {
	return &protos.OrderResponse{
		Code:      200,
		Message:   "Success",
		OrderName: "Order_1",
	}, nil
}
