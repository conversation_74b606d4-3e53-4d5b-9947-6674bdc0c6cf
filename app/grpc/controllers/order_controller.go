package controllers

import (
	"context"
	"goravel/app/grpc/protos/orders"
)

type OrderController struct {
	orders.UnimplementedOrderServer
}

func NewOrderController() *OrderController {
	return &OrderController{}
}

func (r *OrderController) Detail(ctx context.Context, req *orders.OrderRequest) (*orders.OrderResponse, error) {
	return &orders.OrderResponse{
		Code:      200,
		Message:   "Success",
		OrderName: "Order_1",
	}, nil
}
