package grpc

import (
	"google.golang.org/grpc"

	"goravel/app/grpc/controllers"
	"goravel/app/grpc/protos"
	"goravel/app/grpc/protos/orders"
)

type Kernel struct {
}

// The application's global GRPC interceptor stack.
// These middleware are run during every request to your application.
func (kernel Kernel) UnaryServerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{}
}

// The application's client interceptor groups.
func (kernel Kernel) UnaryClientInterceptorGroups() map[string][]grpc.UnaryClientInterceptor {
	return map[string][]grpc.UnaryClientInterceptor{}
}

// Register gRPC services
func (kernel Kernel) RegisterServices(server *grpc.Server) {
	// Register User service
	userController := controllers.NewUserController()
	protos.RegisterUserServer(server, userController)

	// Register Order service
	orderController := controllers.NewOrderController()
	orders.RegisterOrderServer(server, orderController)
}
