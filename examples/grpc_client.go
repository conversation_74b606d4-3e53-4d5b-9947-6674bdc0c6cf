package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"goravel/app/grpc/protos"
)

func main() {
	// Connect to gRPC server
	conn, err := grpc.Dial("127.0.0.1:50051", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("Failed to connect to gRPC server: %v", err)
	}
	defer conn.Close()

	// Create client
	client := protos.NewUserClient(conn)

	// Test cases
	testCases := []int64{1, 2, 3, 0, -1}

	for _, userID := range testCases {
		fmt.Printf("\n=== Testing User ID: %d ===\n", userID)
		
		// Create context with timeout
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// Make request
		req := &protos.UserRequest{
			Id: userID,
		}

		resp, err := client.Show(ctx, req)
		if err != nil {
			log.Printf("Error calling Show: %v", err)
			continue
		}

		// Print response
		fmt.Printf("Response:\n")
		fmt.Printf("  Code: %d\n", resp.GetCode())
		fmt.Printf("  Message: %s\n", resp.GetMessage())
		fmt.Printf("  Name: %s\n", resp.GetName())
	}

	fmt.Println("\n=== gRPC Client Demo Completed ===")
}
